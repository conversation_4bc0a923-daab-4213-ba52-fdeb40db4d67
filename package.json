{"name": "celusio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-separator": "^1.1.2", "@react-spring/web": "^9.7.5", "@tabler/icons-react": "^3.30.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.10", "lucide-react": "^0.477.0", "motion": "^12.4.10", "next": "15.2.1", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4.0.9", "typescript": "^5"}}