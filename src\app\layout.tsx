import type { Metadata } from "next";
import "./globals.css";
import { font } from "@/utils/font";

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON>",
  description: "The growth that your brand needs.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        suppressHydrationWarning
        className={`antialiased grainy-dark ${font.urbanist.className}`}
      >
        {children}
      </body>
    </html>
  );
}
